package com.yxt.order.assistant.server.knowledge.swagger.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * Swagger文档根对象
 */
@Data
public class SwaggerDoc {
    
    /**
     * Swagger版本
     */
    private String swagger;
    
    /**
     * API基本信息
     */
    private Info info;
    
    /**
     * 主机地址
     */
    private String host;
    
    /**
     * 基础路径
     */
    private String basePath;
    
    /**
     * 标签列表
     */
    private List<Tag> tags;
    
    /**
     * API路径定义
     */
    private Map<String, Map<String, Operation>> paths;
    
    /**
     * 数据模型定义
     */
    private Map<String, Definition> definitions;
    
    /**
     * API基本信息
     */
    @Data
    public static class Info {
        private String title;
        private String description;
        private String version;
        private Contact contact;
    }
    
    /**
     * 联系信息
     */
    @Data
    public static class Contact {
        private String name;
        private String email;
        private String url;
    }
    
    /**
     * 标签
     */
    @Data
    public static class Tag {
        private String name;
        private String description;
    }
    
    /**
     * API操作
     */
    @Data
    public static class Operation {
        private List<String> tags;
        private String summary;
        private String description;
        private String operationId;
        private List<String> consumes;
        private List<String> produces;
        private List<Parameter> parameters;
        private Map<String, Response> responses;
        private boolean deprecated;
    }
    
    /**
     * 参数
     */
    @Data
    public static class Parameter {
        private String name;
        private String in;
        private String description;
        private boolean required;
        private String type;
        private String format;
        private Schema schema;
    }
    
    /**
     * 响应
     */
    @Data
    public static class Response {
        private String description;
        private Schema schema;
    }
    
    /**
     * 数据模式
     */
    @Data
    public static class Schema {
        private String type;
        private String format;
        @JsonProperty("$ref")
        @SerializedName("$ref")
        private String ref;
        private Schema items;
        private Map<String, Object> additionalProperties;
    }
    
    /**
     * 数据模型定义
     */
    @Data
    public static class Definition {
        private String type;
        private String title;
        private String description;
        private List<String> required;
        private Map<String, Property> properties;
    }
    
    /**
     * 属性
     */
    @Data
    public static class Property {
        private String type;
        private String format;
        private String description;
        @JsonProperty("$ref")
        @SerializedName("$ref")
        private String ref;
        private Property items;
        private List<String> enumValues;
        private Object example;
    }
}
