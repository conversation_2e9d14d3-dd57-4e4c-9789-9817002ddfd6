package com.yxt.order.assistant.server.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.google.common.collect.Lists;
import com.yxt.lang.util.JsonUtils;
import com.yxt.order.assistant.server.repository.enums.KnowledgeBaseSource;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@TableName("knowledge_base")
public class KnowledgeBase {


  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 知识库来源 CF - confluence文档 Swagger - 接口Swagger
   *
   * @see KnowledgeBaseSource
   */
  private String source;


  /**
   * 知识库名
   */
  private String name;


  /**
   * 拓展信息，灵活使用，存储JSON
   */
  private String extendJson;

  /**
   * 映射到Dify的知识库配置
   */
  private String mappingDifyConfig;

  /**
   * 状态 NORMAL-正常 DELETED-删除
   */
  private String status;

  /**
   * 知识库更新状态：UPDATING-更新中 SUCCESS-更新成功 FAIL-更新失败
   */
  private String updateStatus;


  public CfConfig fetchCfExtendJson() {
    String extendJson = this.getExtendJson();
    CfConfig cfPullConfig = JsonUtils.toObject(extendJson, CfConfig.class);
    Assert.notNull(cfPullConfig, "CF 拉取配置不能为空");
    Assert.notNull(cfPullConfig.getRootPageId(), "CF 根页面 ID 不能为空");
    return cfPullConfig;
  }

  /**
   * 解析知识库
   *
   * @return
   */
  public DataSet fetchDataSet() {
    String difyConfigJson = this.getMappingDifyConfig();
    DataSet dataSet = JsonUtils.toObject(difyConfigJson, DataSet.class);
    Assert.notNull(dataSet, " 拉取配置不能为空");
    Assert.notNull(dataSet.getDataSetId(), "dataSetId 拉取配置不能为空");
    return dataSet;
  }

  public DatabaseConfig fetchDbConfigFromJson() {
    String extendJson = this.getExtendJson();
    return JsonUtils.toObject(extendJson, DatabaseConfig.class);
  }
  public SwaggerConfig fetchSwaggerFromJson() {
    String extendJson = this.getExtendJson();
    return JsonUtils.toObject(extendJson, SwaggerConfig.class);
  }

  public static void main(String[] args) {

    DatabaseConfig dbConfigFromJson = new DatabaseConfig();

    DataSourceConfig dscloud_order_assistant = new DataSourceConfig();
    dscloud_order_assistant.setName("dscloud_order_assistant");
    dscloud_order_assistant.setUrl(
        "**************************************************************************************************************************************************************");
    dscloud_order_assistant.setUsername("agent");
    dscloud_order_assistant.setPassword("WrHNOhOGHR8yzMEgKvao");

    DataSourceConfig dscloud = new DataSourceConfig();
    dscloud.setName("dscloud");
    dscloud.setUrl(
        "**********************************************************************************************************************************************");
    dscloud.setUsername("agent");
    dscloud.setPassword("WrHNOhOGHR8yzMEgKvao");

    DataSourceConfig dscloudOffline = new DataSourceConfig();
    dscloudOffline.setName("dscloud_offline");
    dscloudOffline.setUrl(
        "******************************************************************************************************************************************************");
    dscloudOffline.setUsername("agent");
    dscloudOffline.setPassword("WrHNOhOGHR8yzMEgKvao");

    dbConfigFromJson.setDataSourceList(
        Lists.newArrayList(dscloud, dscloud_order_assistant, dscloudOffline));

//    System.out.println(JsonUtils.toJson(dbConfigFromJson));


    SwaggerConfig swaggerConfig = new SwaggerConfig();
    swaggerConfig.setSwaggerUrlList(Lists.newArrayList("http://hydee-business-order.svc.k8s.test.hxyxt.com/v2/api-docs","http://localhost:8084/v2/api-docs"));
    System.out.println(JsonUtils.toJson(swaggerConfig));
  }

  @Data
  public static class CfConfig {

    private String rootPageId;// 拉取的根页面Id
  }

  @Data
  public static class SwaggerConfig {

    private List<String> swaggerUrlList;// 拉取的根页面Id
  }




  @Data
  public static class DataSet {

    private String dataSetId;//  知识库Id
  }


  @Data
  public static class DatabaseConfig {

    /**
     * 数据源配置列表（动态数据源） 如果提供此参数，将使用动态数据源而不是配置文件中的数据源
     */
    private List<DataSourceConfig> dataSourceList;


  }

  /**
   * 数据源配置信息
   */
  @Data
  public static class DataSourceConfig {

    /**
     * 数据源名称/别名
     */
    private String name;

    /**
     * 数据库连接URL
     */
    private String url;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 驱动类名（可选，默认使用MySQL驱动）
     */
    private String driverClassName = "com.mysql.cj.jdbc.Driver";

    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectionTimeout = 30000;

    /**
     * 最大连接数
     */
    private Integer maximumPoolSize = 5;

    /**
     * 最小空闲连接数
     */
    private Integer minimumIdle = 1;

    /**
     * 连接最大存活时间（毫秒）
     */
    private Long maxLifetime = 1800000L; // 30分钟

    /**
     * 空闲连接超时时间（毫秒）
     */
    private Long idleTimeout = 600000L; // 10分钟

    /**
     * 验证连接的SQL语句
     */
    private String validationQuery = "SELECT 1";

    /**
     * 是否测试连接
     */
    private Boolean testConnection = true;

    /**
     * 获取数据库名称（从URL中提取）
     */
    public String getDatabaseName() {
      if (url == null) {
        return null;
      }

      try {
        // 从URL中提取数据库名称
        // 例如：********************************************* -> test
        String[] parts = url.split("/");
        if (parts.length >= 4) {
          String dbPart = parts[3];
          // 去掉参数部分
          int paramIndex = dbPart.indexOf("?");
          if (paramIndex > 0) {
            dbPart = dbPart.substring(0, paramIndex);
          }
          return dbPart;
        }
      } catch (Exception e) {
        // 忽略解析错误
      }

      return "unknown";
    }

    /**
     * 验证配置是否完整
     */
    public boolean isValid() {
      return name != null && !name.trim().isEmpty() &&
          url != null && !url.trim().isEmpty() &&
          username != null && !username.trim().isEmpty() &&
          password != null;
    }
  }
}
