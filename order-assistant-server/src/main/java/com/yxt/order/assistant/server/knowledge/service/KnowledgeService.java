package com.yxt.order.assistant.server.knowledge.service;

import com.yxt.order.assistant.server.knowledge.req.CreateKnowledgeBaseReq;
import com.yxt.order.assistant.server.knowledge.req.DeleteKnowledgeBaseDocumentReq;
import com.yxt.order.assistant.server.knowledge.req.UploadKnowledgeBaseDocumentReq;
import com.yxt.order.assistant.server.repository.entity.KnowledgeBase;
import com.yxt.order.assistant.server.repository.enums.KnowledgeBaseSource;

public interface KnowledgeService {

  KnowledgeBase detail(Long id);

  void pullCfKnowledge(KnowledgeBase knowledgeBase);

  void pullSwaggerKnowledge(Long knowledgeBaseId);

  void pullDatabaseKnowledge(KnowledgeBase knowledgeBase);

  void upload(UploadKnowledgeBaseDocumentReq req);

  void delete(DeleteKnowledgeBaseDocumentReq req);

  void insertOrUpdate(String title,String uniqueKey,
      String content,
      Long knowledgeBaseId,
      KnowledgeBaseSource source
  );

  void create(<PERSON><PERSON>KnowledgeBaseReq req);
}
