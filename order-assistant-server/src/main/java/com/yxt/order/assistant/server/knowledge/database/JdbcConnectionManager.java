package com.yxt.order.assistant.server.knowledge.database;

import com.yxt.order.assistant.server.repository.entity.KnowledgeBase.DataSourceConfig;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * JDBC连接管理器
 * 负责管理动态数据源的连接池
 */
@Slf4j
@Component
public class JdbcConnectionManager {
    
    /**
     * 数据源缓存
     */
    private final ConcurrentMap<String, HikariDataSource> dataSourceCache = new ConcurrentHashMap<>();
    
    /**
     * 获取数据源连接
     */
    public Connection getConnection(DataSourceConfig config) throws SQLException {
        String cacheKey = generateCacheKey(config);
        
        HikariDataSource dataSource = dataSourceCache.computeIfAbsent(cacheKey, key -> {
            try {
                return createDataSource(config);
            } catch (Exception e) {
                log.error("创建数据源失败: {}", config.getName(), e);
                throw new RuntimeException("创建数据源失败: " + config.getName(), e);
            }
        });
        
        return dataSource.getConnection();
    }
    
    /**
     * 测试数据源连接
     */
    public boolean testConnection(DataSourceConfig config) {
        try (Connection connection = getConnection(config)) {
            return connection.isValid(config.getConnectionTimeout() / 1000);
        } catch (Exception e) {
            log.warn("测试数据源连接失败: {}", config.getName(), e);
            return false;
        }
    }
    
    /**
     * 创建数据源
     */
    private HikariDataSource createDataSource(DataSourceConfig config) {
        log.info("创建数据源: {}", config.getName());
        
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setJdbcUrl(config.getUrl());
        hikariConfig.setUsername(config.getUsername());
        hikariConfig.setPassword(config.getPassword());
        hikariConfig.setDriverClassName(config.getDriverClassName());
        
        // 连接池配置
        hikariConfig.setMaximumPoolSize(config.getMaximumPoolSize());
        hikariConfig.setMinimumIdle(config.getMinimumIdle());
        hikariConfig.setConnectionTimeout(config.getConnectionTimeout());
        hikariConfig.setMaxLifetime(config.getMaxLifetime());
        hikariConfig.setIdleTimeout(config.getIdleTimeout());
        
        // 连接验证
        if (config.getValidationQuery() != null) {
            hikariConfig.setConnectionTestQuery(config.getValidationQuery());
        }
        
        // 连接池名称
        hikariConfig.setPoolName("DatabaseSchema-" + config.getName());
        
        // 其他配置
        hikariConfig.setAutoCommit(true);
        hikariConfig.setReadOnly(true); // 只读连接，提高安全性
        
        return new HikariDataSource(hikariConfig);
    }
    
    /**
     * 生成缓存键
     */
    private String generateCacheKey(DataSourceConfig config) {
        return String.format("%s_%s_%s", 
                config.getName(), 
                config.getUrl(), 
                config.getUsername());
    }

    
    /**
     * 关闭所有数据源
     */
    public void closeAllDataSources() {
        log.info("关闭所有数据源连接池");
        dataSourceCache.values().forEach(dataSource -> {
            try {
                dataSource.close();
            } catch (Exception e) {
                log.error("关闭数据源失败", e);
            }
        });
        dataSourceCache.clear();
    }

}
