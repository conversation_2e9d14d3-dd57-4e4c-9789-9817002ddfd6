package com.yxt.order.assistant.bootstrap;

import io.github.imfangs.dify.client.DifyDatasetsClient;
import io.github.imfangs.dify.client.exception.DifyApiException;
import io.github.imfangs.dify.client.model.datasets.CreateDocumentByTextRequest;
import io.github.imfangs.dify.client.model.datasets.DatasetResponse;
import io.github.imfangs.dify.client.model.datasets.DocumentResponse;
import io.github.imfangs.dify.client.model.datasets.ProcessRule;
import io.github.imfangs.dify.client.model.datasets.RetrievalModel;
import io.github.imfangs.dify.client.model.datasets.UpdateDocumentByTextRequest;
import java.io.IOException;
import java.util.UUID;
import javax.annotation.Resource;
import org.junit.Test;

public class DataSetTest extends BaseTest{

  @Resource
  private DifyDatasetsClient difyDatasetsClient;

  private String dataSetId = "4f215f8e-406d-43bc-ad83-5b5047c72009";

  @Test
  public void testDataSetDetail() throws DifyApiException, IOException {
    DatasetResponse dataset = difyDatasetsClient.getDataset(dataSetId);
    System.out.println();
  }

  @Test
  public void test1() throws DifyApiException, IOException {
    RetrievalModel retrievalModel = new RetrievalModel();
    retrievalModel.setSearchMethod("hybrid_search");
    retrievalModel.setRerankingEnable(false);
    retrievalModel.setTopK(2);
    retrievalModel.setScoreThresholdEnabled(false);

    CreateDocumentByTextRequest request = CreateDocumentByTextRequest.builder()
        .name("测试文档-" + System.currentTimeMillis())
        .text("这是一个测试文档的内容。\n这是第二行内容。\n这是第三行内容。")
        .indexingTechnique("economy")
        .docForm("text_model")
        // 1.1.3 invalid_param (400) - Must not be null! 【doc_language】
        .docLanguage("Chinese")
        // 1.1.3 invalid_param (400) - Must not be null! 【retrieval_model】
        .retrievalModel(retrievalModel)
        // 没有这里的设置，会500报错，服务器内部错误
        .processRule(ProcessRule.builder().mode("automatic").build())
        .build();

    DocumentResponse documentByText = difyDatasetsClient.createDocumentByText(dataSetId, request);
  }

  @Test
  public void test1Update() throws DifyApiException, IOException {
    String docId="1b201e02-4fb6-4cb5-aedf-e29f2e4ec9d6";

    UpdateDocumentByTextRequest request = UpdateDocumentByTextRequest.builder()
        .name("knowledge.getTargetName()"+ UUID.randomUUID().toString()).text("knowledge.getContent()").build();
    DocumentResponse documentResponse = difyDatasetsClient.updateDocumentByText(dataSetId, docId,
        request);
    System.out.println();

  }
}
